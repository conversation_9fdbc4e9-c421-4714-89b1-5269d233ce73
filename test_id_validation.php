<?php

// Simple test to demonstrate the validation changes
echo "=== Testing UPDATED validation (accepts string/float/integer) ===\n";

// Simulate Laravel validation behavior
function testValidation($value, $rule) {
    if ($rule === 'required') {
        return !empty($value) || $value === 0 || $value === '0';
    }
    return true;
}

// Test different ID types
$testCases = [
    ['id' => 123, 'type' => 'integer'],
    ['id' => '123', 'type' => 'string'],
    ['id' => 123.5, 'type' => 'float'],
    ['id' => '123.5', 'type' => 'string float'],
    ['id' => 'abc123', 'type' => 'alphanumeric string'],
    ['id' => 'user-id-123', 'type' => 'hyphenated string'],
];

foreach ($testCases as $test) {
    $passes = testValidation($test['id'], 'required');
    echo "ID: '{$test['id']}' ({$test['type']}) - " . ($passes ? "✅ PASSES" : "❌ FAILS") . "\n";
}

echo "\n=== Database Conversion Logic ===\n";
foreach ($testCases as $test) {
    $id = $test['id'];
    $converted = is_numeric($id) ? (int) $id : $id;
    echo "Original: '{$id}' -> Converted: '{$converted}' (type: " . gettype($converted) . ")\n";
}

echo "\n=== Summary of Changes ===\n";
echo "✅ Changed validation from 'required|integer' to 'required' (accepts any type)\n";
echo "✅ Now accepts integers, strings, floats, and any other type\n";
echo "✅ Added smart conversion: numeric values → integer, non-numeric → keep as-is\n";
echo "✅ Mobile apps can now send any format of ID\n";
