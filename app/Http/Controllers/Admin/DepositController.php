<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BalanceManager;
use App\Models\Customer;
use App\Models\Merchant;
use App\Models\PaymentRequest;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\DataTables;

class DepositController extends Controller
{
    /**
     * Show payment requests with filters and DataTable server-side processing
     */
    public function index(Request $request)
    {
        // If it's an AJAX request (DataTables)
        if ($request->ajax()) {
            $query = PaymentRequest::with(['agent', 'merchant'])
            ->orderBy('created_at', 'desc'); // latest first

            // Filter by merchant
            if ($request->merchant_id) {
                $merchant = Merchant::find($request->merchant_id);
                if ($merchant) {
                    if ($merchant->merchant_type == 'general') {
                        $query->where('merchant_id', $merchant->id);
                    } else {
                        $query->where('sub_merchant', $merchant->id);
                    }
                }
            }

            // Filter by payment method, number, trxid, status, reference, dates
            if ($request->mfs) $query->where('payment_method', $request->mfs);
            if ($request->method_number) $query->where('sim_id', $request->method_number);
            if ($request->trxid) $query->where('payment_method_trx', $request->trxid);
            if ($request->cust_name) {
                $query->where(function ($q) use ($request) {
                    $q->where('cust_name', 'like', "%{$request->cust_name}%")
                      ->orWhere('cust_phone', 'like', "%{$request->cust_name}%");
                });
            }
            if ($request->status) {
                if ($request->status == 'pending') $query->where('status', 0);
                else $query->where('status', $request->status);
            }
            if ($request->reference) $query->where('reference', $request->reference);
            if ($request->start_date && $request->end_date) {
                $query->whereDate('created_at', '>=', $request->start_date)
                      ->whereDate('created_at', '<=', $request->end_date);
            }

            // Return data to DataTables
            return DataTables::of($query)
                ->addIndexColumn()
               ->addColumn('merchant_name', function ($row) {
                $designation = '';
                $name = '';

                if ($row->customer_id) {
                    $customer = CustomerInfo($row->customer_id);
                    $name = $customer->customer_name ?? $row->cust_name;
                    $designation = 'Customer';
                } elseif ($row->sub_merchant != null) {
                    $Merchant = \App\Models\Merchant::find($row->sub_merchant);
                    $name = $Merchant->fullname ?? 'N/A';
                    $designation = 'Sub Merchant';
                } elseif ($row->merchant_id != null) {
                    $Merchant = \App\Models\Merchant::find($row->merchant_id);
                    $name = $Merchant->fullname ?? 'N/A';
                    $designation = 'Merchant';
                }

                $reference = $row->reference ? "<br><span class='text-info'>{$row->reference}</span>" : '';

                return "{$name} <br> <span class='text-success font-weight-bold'>{$designation}</span> {$reference}";
            })
            ->editColumn('payment_method', function ($row) {
                // Get payment method and sim_id
                $method = $row->payment_method;
                $simId = $row->sim_id;

                // Determine make_method
               $make_method = '';
                $get_method = \App\Models\payment_method::where('sim_id', $row->sim_id)->first();
                $make_method = 'Cashout';
                if ($get_method->type == 'agent') {
                    $make_method = 'Cashout';
                } elseif ($get_method->type == 'personal') {
                    $make_method = 'Send Money';
                } elseif ($get_method->type == ' customer') {
                    $make_method = 'Payment';
                }

                // Override if P2C
                if ($row->payment_type == 'P2C') {
                    $make_method = 'Payment';
                }

                // Return combined HTML
                return "{$method}, {$simId} <br> {$make_method}";
            })

                ->addColumn('from_number', function ($row) {
                    $BMData = BalanceManager::where('trxid', $row->payment_method_trx)->first();
                    return $BMData->mobile ?? $row->from_number;
                })
                ->addColumn('note', function ($row) {
                    return $row->note ?? $row->reject_msg ?? '-';
                })
                ->addColumn('status_html', function ($row) {
                    switch ($row->status) {
                        case 0:
                            return "<span class='badge badge-pill bg-warning text-white'>Pending</span>";
                        case 1:
                            return "<span class='badge badge-pill bg-success text-white'>
                                        <i class='bx bx-radio-circle-marked bx-burst bx-rotate-90 align-middle font-18 me-1'></i>Success
                                    </span>";
                        case 2:
                            $acceptedBy = $row->accepted_by ? "<br>{$row->accepted_by}" : '';
                            return "<span class='badge badge-pill bg-success text-white'>
                                        <i class='bx bx-radio-circle-marked bx-burst bx-rotate-90 align-middle font-18 me-1'></i>Approved
                                    </span>{$acceptedBy}";
                        case 3:
                            return "<span class='badge badge-pill bg-danger text-white'>Rejected</span>";
                        case 4:
                            return "<span class='badge badge-pill bg-danger text-white'>Spam</span>";
                        default:
                            return "-";
                    }
                })
                ->addColumn('action', function ($row) {
                    $btn = '';

                    if ($row->status == 0 || $row->status == 4) {
                        // Reject button
                        $btn .= '<a href="#" data-payment-id="'.$row->id.'" class="rejectPaymentBtn openPopup btn btn-sm btn-outline-danger">
                                    <i class="lni lni-cross-circle" aria-hidden="true"></i>
                                </a> ';

                        // Approve button
                        $btn .= '<button type="button" data-payment-id="'.$row->id.'" class="spamPaymentBtn btn btn-sm btn-success">
                                    <i class="bx bx-check-double"></i> Approve
                                </button>';
                    } elseif ($row->status == 3) {
                        // Pending button with confirm
                        $btn .= '<a href="'.route('pending-payment-request', ['id' => $row->id]).'" 
                                    class="pendingPaymentBtn openPopup btn btn-sm btn-outline-danger" 
                                    onclick="return confirm(\'Are you sure you want to mark this payment as pending?\');" 
                                    title="Mark as Pending">
                                    <i class="bx bx-hourglass" aria-hidden="true"></i>
                                </a>';
                    }

                    return $btn;
                })
                ->addColumn('dates', function ($row) {
                    $created = $row->created_at ? $row->created_at->format('h:i:sa, d-m-Y') : '-';
                    $createdAgo = $row->created_at ? \Carbon\Carbon::parse($row->created_at)->diffForHumans() : '-';

                    $updated = $row->updated_at ? $row->updated_at->format('h:i:sa, d-m-Y') : '-';
                    $updatedAgo = $row->updated_at ? \Carbon\Carbon::parse($row->updated_at)->diffForHumans() : '-';

                    return "
                        {$created} <br>
                        <span class='text-success font-weight-bold'>{$createdAgo}</span> <br>
                        {$updated} <br>
                        <span class='text-success font-weight-bold'>{$updatedAgo}</span>
                    ";
                })
                ->rawColumns(['merchant_name','status_html', 'action','payment_method','dates'])
                ->make(true);
        }

        // Normal page load, pass merchants for filter dropdown
        $merchants = Merchant::orderBy('fullname')->get();

        return view('admin.deposit.index', compact('merchants'));
    }


    public function reject_deposit_request(Request $request)
{
    // Validate input
    $request->validate([
        'transId' => 'required|integer|exists:payment_requests,id',
        'reason'  => 'required|string|max:255',
    ]);

    try {
        // Find payment
        $payment = PaymentRequest::find($request->transId);

        // Update status and reject message
        $payment->update([
            'status'     => 3, // Rejected
            'reject_msg' => $request->reason,
        ]);

        // Call webhook (if exists)
        if (function_exists('merchantWebHook')) {
            merchantWebHook($payment->reference);
        }

        // Return JSON
        return response()->json([
            'status'  => 200,
            'message' => 'Payment request rejected successfully.',
        ]);

    } catch (\Exception $e) {
        return response()->json([
            'status'  => 500,
            'message' => 'Something went wrong: ' . $e->getMessage(),
        ]);
    }
}

public function approve_deposit_request(Request $request)
{
    $request->validate([
        'payment_id' => 'required|exists:payment_requests,id',
        'payment_method_trx' => 'required|string|max:255',
        'amount' => 'sometimes|numeric'
    ]);

    $payment = PaymentRequest::where('id', $request->payment_id)
        ->whereIn('status', [0,4]) // only pending/spam
        ->first();

    if (!$payment) {
        return response()->json([
            'success' => false,
            'message' => 'Payment not found or not eligible for approval',
        ], 404);
    }

    // ✅ prevent duplicate trx
    $exists = PaymentRequest::where('payment_method_trx', $request->payment_method_trx)
        ->whereIn('status', [1,2])
        ->first();

    if ($exists) {
        return response()->json([
            'success' => false,
            'message' => 'This transaction is already approved',
        ], 409);
    }

    // ✅ approve logic
    $payment->status = 2; // approved
    $payment->payment_method_trx = $request->payment_method_trx;

    if ($request->amount) {
        $payment->amount = $request->amount;
    }

    if ($payment->save()) {
        merchantWebHook($payment->reference);

        return response()->json([
            'success' => true,
            'payment_id' => $payment->id,
            'message' => 'Payment approved successfully',
        ]);
    }

    return response()->json([
        'success' => false,
        'message' => 'Failed to update payment status',
    ], 500);
}


}

